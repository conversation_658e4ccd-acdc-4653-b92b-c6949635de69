import { $get, $post } from '@/utils/request'

/**
 * 人机验证行为校验接口
 * @param {Object} data - 请求参数
 * @param {string} data.phone - 用户手机号
 * @param {string} data.template - 模板信息（可选）
 * @param {string} data.channelId - 渠道ID
 * @param {string} data.captchaVerifyParam - 阿里云人机验证返回的验证参数
 * @returns {Promise} 返回验证结果
 */
export const behaviorVerify = (data = {}) => {
  return $post({
    url: '/user/behaviorVerify',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

/**
 * 人机验证登录接口（替换原有的短信验证码登录）
 * @param {Object} data - 请求参数
 * @param {string} data.phone - 用户手机号
 * @param {string} data.channelId - 渠道ID
 * @param {number} data.demandAmount - 需求金额
 * @param {string} data.deviceType - 设备类型
 * @param {string} data.phoneBlack - 黑名单手机号信息
 * @param {string} data.captchaVerifyParam - 人机验证参数（替换原有的code参数）
 * @returns {Promise} 返回登录结果，包含用户ID等信息
 */
export const saveLoanWithCaptcha = (data = {}) => {
  return $post({
    url: '/saveLoan',
    data,
    isEncrypt: true,
    deEncrypt: false
  })
}
